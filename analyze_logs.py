#!/usr/bin/env python3
"""
日志分析脚本 - 分析Celery定时任务执行情况
"""
import os
import re
import sys
from datetime import datetime, timedelta
from collections import defaultdict
import subprocess

class LogAnalyzer:
    def __init__(self):
        self.log_dir = "logs"
        self.beat_log = os.path.join(self.log_dir, "celery_beat.log")
        self.worker_log = os.path.join(self.log_dir, "celery_worker.log")
        self.django_log = os.path.join(self.log_dir, "django.log")
        
    def print_header(self, title):
        """打印标题"""
        print("\n" + "=" * 80)
        print(f"📊 {title}")
        print("=" * 80)
    
    def analyze_beat_log(self):
        """分析Beat日志"""
        self.print_header("Celery Beat 日志分析")
        
        if not os.path.exists(self.beat_log):
            print("❌ Beat日志文件不存在")
            return
        
        with open(self.beat_log, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📄 日志文件: {self.beat_log}")
        print(f"📊 总行数: {len(lines)}")
        
        # 分析启动时间
        startup_times = []
        scheduled_tasks = []
        
        for line in lines:
            # 查找启动时间
            if "beat: Starting..." in line:
                match = re.search(r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                if match:
                    startup_times.append(match.group(1))
            
            # 查找调度任务
            if "Scheduler: Sending due task" in line and "每日天气邮件发送" in line:
                match = re.search(r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                if match:
                    scheduled_tasks.append(match.group(1))
        
        print(f"\n🚀 Beat启动次数: {len(startup_times)}")
        for i, time in enumerate(startup_times, 1):
            print(f"  {i}. {time}")
        
        print(f"\n📧 邮件任务调度次数: {len(scheduled_tasks)}")
        for i, time in enumerate(scheduled_tasks, 1):
            print(f"  {i}. {time}")
        
        # 分析是否在6点调度
        morning_schedules = [t for t in scheduled_tasks if " 06:0" in t]
        print(f"\n⏰ 早上6点调度次数: {len(morning_schedules)}")
        for time in morning_schedules:
            print(f"  ✅ {time}")
        
        if not morning_schedules:
            print("  ❌ 没有发现早上6点的调度记录")
    
    def analyze_worker_log(self):
        """分析Worker日志"""
        self.print_header("Celery Worker 日志分析")
        
        if not os.path.exists(self.worker_log):
            print("❌ Worker日志文件不存在")
            return
        
        with open(self.worker_log, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📄 日志文件: {self.worker_log}")
        print(f"📊 总行数: {len(lines)}")
        
        # 分析任务执行
        task_executions = []
        email_results = []
        
        for line in lines:
            # 查找任务接收
            if "Task subscriptions.tasks.send_daily_weather_emails" in line and "received" in line:
                match = re.search(r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                task_id_match = re.search(r'\[([a-f0-9-]+)\]', line)
                if match and task_id_match:
                    task_executions.append({
                        'time': match.group(1),
                        'task_id': task_id_match.group(1)
                    })
            
            # 查找邮件发送结果
            if "邮件发送完成:" in line:
                match = re.search(r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                result_match = re.search(r'成功 (\d+), 失败 (\d+)', line)
                if match and result_match:
                    email_results.append({
                        'time': match.group(1),
                        'success': int(result_match.group(1)),
                        'failed': int(result_match.group(2))
                    })
        
        print(f"\n📧 邮件任务执行次数: {len(task_executions)}")
        for i, task in enumerate(task_executions, 1):
            print(f"  {i}. {task['time']} - 任务ID: {task['task_id'][:8]}...")
        
        print(f"\n📊 邮件发送结果:")
        total_success = 0
        total_failed = 0
        for i, result in enumerate(email_results, 1):
            print(f"  {i}. {result['time']} - 成功: {result['success']}, 失败: {result['failed']}")
            total_success += result['success']
            total_failed += result['failed']
        
        print(f"\n📈 总计: 成功 {total_success}, 失败 {total_failed}")
    
    def analyze_process_history(self):
        """分析进程历史"""
        self.print_header("进程历史分析")
        
        try:
            # 检查当前进程
            result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
            lines = result.stdout.split('\n')
            
            current_workers = []
            current_beats = []
            
            for line in lines:
                if 'celery' in line and 'worker' in line and 'weatherblog' in line:
                    current_workers.append(line.strip())
                elif 'celery' in line and 'beat' in line and 'weatherblog' in line:
                    current_beats.append(line.strip())
            
            print(f"🔍 当前运行的Celery进程:")
            print(f"  Worker进程: {len(current_workers)} 个")
            for i, process in enumerate(current_workers, 1):
                # 提取启动时间
                parts = process.split()
                if len(parts) > 8:
                    start_time = parts[8]
                    print(f"    {i}. 启动时间: {start_time}")
            
            print(f"  Beat进程: {len(current_beats)} 个")
            for i, process in enumerate(current_beats, 1):
                parts = process.split()
                if len(parts) > 8:
                    start_time = parts[8]
                    print(f"    {i}. 启动时间: {start_time}")
            
            if len(current_beats) == 0:
                print("  ❌ 当前没有Beat进程运行！")
            elif len(current_beats) > 1:
                print("  ⚠️ 发现多个Beat进程，可能导致重复执行")
            else:
                print("  ✅ Beat进程数量正常")
                
        except Exception as e:
            print(f"❌ 检查进程失败: {e}")
    
    def analyze_system_logs(self):
        """分析系统日志中的相关信息"""
        self.print_header("系统日志分析")
        
        try:
            # 检查最近的系统日志中是否有进程被杀死的记录
            result = subprocess.run([
                'journalctl', '--since', '2 days ago', 
                '--grep', 'celery|killed|oom'
            ], capture_output=True, text=True)
            
            if result.stdout.strip():
                print("🔍 发现相关系统日志:")
                for line in result.stdout.strip().split('\n')[:10]:  # 只显示前10行
                    print(f"  {line}")
            else:
                print("✅ 没有发现相关的系统日志")
                
        except Exception as e:
            print(f"⚠️ 无法检查系统日志: {e}")
    
    def analyze_timing_issues(self):
        """分析时间相关问题"""
        self.print_header("时间分析")
        
        # 检查系统时间
        try:
            result = subprocess.run(['date'], capture_output=True, text=True)
            print(f"🕐 当前系统时间: {result.stdout.strip()}")
            
            result = subprocess.run(['timedatectl', 'status'], capture_output=True, text=True)
            print(f"🌍 时区信息:")
            for line in result.stdout.strip().split('\n'):
                if 'Time zone' in line or 'Local time' in line:
                    print(f"  {line.strip()}")
                    
        except Exception as e:
            print(f"❌ 检查时间失败: {e}")
        
        # 分析预期的执行时间
        now = datetime.now()
        today_6am = now.replace(hour=6, minute=0, second=0, microsecond=0)
        tomorrow_6am = today_6am + timedelta(days=1)
        
        print(f"\n⏰ 定时任务分析:")
        print(f"  今天6点: {today_6am.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"  明天6点: {tomorrow_6am.strftime('%Y-%m-%d %H:%M:%S')}")
        
        if now > today_6am:
            print(f"  📊 今天6点已过去 {now - today_6am}")
        else:
            print(f"  📊 距离今天6点还有 {today_6am - now}")
    
    def generate_recommendations(self):
        """生成建议"""
        self.print_header("问题诊断和建议")
        
        print("🔍 基于日志分析的发现:")
        print()
        
        # 检查日志文件大小
        for log_file in [self.beat_log, self.worker_log]:
            if os.path.exists(log_file):
                size = os.path.getsize(log_file)
                print(f"📄 {log_file}: {size} 字节")
                if size > 10 * 1024 * 1024:  # 10MB
                    print(f"  ⚠️ 日志文件较大，建议设置日志轮转")
        
        print("\n💡 建议解决方案:")
        print("1. 🔄 设置进程监控脚本，每10分钟检查一次")
        print("2. 📋 使用systemd管理Celery服务，实现自动重启")
        print("3. 📊 设置日志轮转，避免日志文件过大")
        print("4. ⏰ 在早上5:50添加预检查，确保6点任务正常")
        print("5. 📧 添加邮件通知，当服务异常时发送告警")
    
    def run_analysis(self):
        """运行完整分析"""
        print("🔍 Celery定时任务日志分析工具")
        print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        self.analyze_beat_log()
        self.analyze_worker_log()
        self.analyze_process_history()
        self.analyze_system_logs()
        self.analyze_timing_issues()
        self.generate_recommendations()
        
        print("\n" + "=" * 80)
        print("✅ 分析完成")
        print("=" * 80)

if __name__ == "__main__":
    analyzer = LogAnalyzer()
    analyzer.run_analysis()
