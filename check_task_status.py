#!/usr/bin/env python
"""
检查定时任务状态
"""
import os
import sys
import django
import subprocess
from datetime import datetime, timedelta

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'weatherblog.settings')
django.setup()

from django_celery_beat.models import PeriodicTask, CrontabSchedule
from django.utils import timezone

def check_processes():
    """检查进程状态"""
    print("🔍 检查Celery进程状态")
    print("=" * 50)
    
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        
        worker_count = 0
        beat_count = 0
        
        for line in lines:
            if 'celery' in line and 'worker' in line:
                worker_count += 1
                print(f"Worker: {line.strip()}")
            elif 'celery' in line and 'beat' in line:
                beat_count += 1
                print(f"Beat: {line.strip()}")
        
        print(f"\n📊 Worker进程: {worker_count} 个")
        print(f"📊 Beat进程: {beat_count} 个")
        
        if beat_count == 0:
            print("❌ 没有Beat进程运行！定时任务不会执行")
            return False
        elif beat_count > 1:
            print("⚠️ 多个Beat进程，可能导致重复执行")
        else:
            print("✅ Beat进程正常")
        
        return beat_count > 0
        
    except Exception as e:
        print(f"❌ 检查进程失败: {e}")
        return False

def check_task_config():
    """检查任务配置"""
    print("\n🔍 检查定时任务配置")
    print("=" * 50)
    
    try:
        # 检查邮件任务
        email_task = PeriodicTask.objects.filter(
            task='subscriptions.tasks.send_daily_weather_emails'
        ).first()
        
        if not email_task:
            print("❌ 没有找到邮件发送定时任务！")
            return False
        
        print(f"任务名称: {email_task.name}")
        print(f"任务状态: {'启用' if email_task.enabled else '禁用'}")
        print(f"Crontab: {email_task.crontab}")
        print(f"最后运行: {email_task.last_run_at}")
        
        if not email_task.enabled:
            print("❌ 定时任务已禁用！")
            return False
        
        # 检查下次执行时间
        now = timezone.now()
        print(f"当前时间: {now}")
        
        # 计算下次6点执行时间
        next_6am = now.replace(hour=6, minute=0, second=0, microsecond=0)
        if now.hour >= 6:
            next_6am += timedelta(days=1)
        
        print(f"下次执行: {next_6am}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查任务配置失败: {e}")
        return False

def check_recent_execution():
    """检查最近的执行记录"""
    print("\n🔍 检查最近执行记录")
    print("=" * 50)
    
    # 检查今天早上6点
    today = datetime.now().strftime('%Y-%m-%d')
    yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    
    log_files = ['logs/celery_worker.log', 'logs/celery_beat.log']
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"\n📄 {log_file}:")
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # 查找今天和昨天的6点执行记录
                found_today = False
                found_yesterday = False
                
                for line in lines:
                    if f'{today} 06:00' in line:
                        print(f"  今天: {line.strip()}")
                        found_today = True
                    elif f'{yesterday} 06:00' in line:
                        print(f"  昨天: {line.strip()}")
                        found_yesterday = True
                
                if not found_today:
                    print(f"  ⚠️ 没有找到今天6点的执行记录")
                if not found_yesterday:
                    print(f"  ⚠️ 没有找到昨天6点的执行记录")
                    
            except Exception as e:
                print(f"  ❌ 读取日志失败: {e}")
        else:
            print(f"⚠️ 日志文件不存在: {log_file}")

def provide_restart_commands():
    """提供重启命令"""
    print("\n🔧 如果需要重启Celery服务")
    print("=" * 50)
    print("执行以下命令:")
    print()
    print("# 1. 停止所有Celery进程")
    print("pkill -f celery")
    print()
    print("# 2. 等待几秒")
    print("sleep 3")
    print()
    print("# 3. 重新启动")
    print("nohup celery -A weatherblog worker --loglevel=info > logs/celery_worker.log 2>&1 &")
    print("nohup celery -A weatherblog beat --loglevel=info > logs/celery_beat.log 2>&1 &")
    print()
    print("# 4. 验证")
    print("ps aux | grep celery")

if __name__ == "__main__":
    print("🔍 定时任务状态检查")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    processes_ok = check_processes()
    config_ok = check_task_config()
    check_recent_execution()
    
    print("\n" + "=" * 50)
    if processes_ok and config_ok:
        print("✅ 定时任务配置正常")
        print("如果今天早上6点没有执行，可能是进程重启后丢失了")
    else:
        print("❌ 发现问题，需要修复")
    
    provide_restart_commands()
