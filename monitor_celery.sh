#!/bin/bash
# Celery进程监控和自动重启脚本

SCRIPT_DIR="/home/<USER>/weather/weatherblog"
LOG_DIR="$SCRIPT_DIR/logs"

# 禁用邮件通知，避免sendmail依赖问题
export MAILTO=""

# 激活虚拟环境
source /home/<USER>/.bashrc
conda activate weather

# 切换到项目目录
cd $SCRIPT_DIR

# 检查Redis是否运行
check_redis() {
    if ! redis-cli ping > /dev/null 2>&1; then
        echo "$(date): ❌ Redis服务未运行，尝试启动..." >> $LOG_DIR/monitor.log
        systemctl start redis
        sleep 2
    fi
}

# 检查Celery Worker
check_worker() {
    WORKER_COUNT=$(ps aux | grep -c "celery.*worker.*weatherblog")
    if [ $WORKER_COUNT -eq 0 ]; then
        echo "$(date): ❌ Celery Worker进程不存在，重新启动..." >> $LOG_DIR/monitor.log
        nohup celery -A weatherblog worker --loglevel=info > $LOG_DIR/celery_worker.log 2>&1 &
        echo "$(date): ✅ Celery Worker已重启" >> $LOG_DIR/monitor.log
    else
        echo "$(date): ✅ Celery Worker运行正常 ($WORKER_COUNT 个进程)" >> $LOG_DIR/monitor.log
    fi
}

# 检查Celery Beat
check_beat() {
    BEAT_COUNT=$(ps aux | grep -c "celery.*beat.*weatherblog")
    if [ $BEAT_COUNT -eq 0 ]; then
        echo "$(date): ❌ Celery Beat进程不存在，重新启动..." >> $LOG_DIR/monitor.log
        nohup celery -A weatherblog beat --loglevel=info > $LOG_DIR/celery_beat.log 2>&1 &
        echo "$(date): ✅ Celery Beat已重启" >> $LOG_DIR/monitor.log
    elif [ $BEAT_COUNT -gt 1 ]; then
        echo "$(date): ⚠️ 发现多个Beat进程 ($BEAT_COUNT 个)，清理重启..." >> $LOG_DIR/monitor.log
        pkill -f "celery.*beat"
        sleep 2
        nohup celery -A weatherblog beat --loglevel=info > $LOG_DIR/celery_beat.log 2>&1 &
        echo "$(date): ✅ Celery Beat已清理并重启" >> $LOG_DIR/monitor.log
    else
        echo "$(date): ✅ Celery Beat运行正常" >> $LOG_DIR/monitor.log
    fi
}

# 检查系统资源
check_resources() {
    # 检查内存使用率
    MEM_USAGE=$(free | grep Mem | awk '{printf("%.1f", $3/$2 * 100.0)}')
    if (( $(echo "$MEM_USAGE > 90" | bc -l) )); then
        echo "$(date): ⚠️ 内存使用率过高: ${MEM_USAGE}%" >> $LOG_DIR/monitor.log
    fi
    
    # 检查磁盘空间
    DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ $DISK_USAGE -gt 90 ]; then
        echo "$(date): ⚠️ 磁盘使用率过高: ${DISK_USAGE}%" >> $LOG_DIR/monitor.log
    fi
}

# 主监控逻辑
main() {
    echo "$(date): 🔍 开始Celery服务监控检查..." >> $LOG_DIR/monitor.log
    
    # 创建日志目录
    mkdir -p $LOG_DIR
    
    # 检查各项服务
    check_redis
    check_worker
    check_beat
    check_resources
    
    echo "$(date): ✅ 监控检查完成" >> $LOG_DIR/monitor.log
    echo "----------------------------------------" >> $LOG_DIR/monitor.log
}

# 执行监控
main
