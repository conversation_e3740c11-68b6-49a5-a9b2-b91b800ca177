#!/bin/bash
# 设置Celery的systemd服务

# 创建Celery Worker服务
cat > /etc/systemd/system/celery-worker.service << 'EOF'
[Unit]
Description=Celery Worker Service
After=network.target redis.service

[Service]
Type=forking
User=root
Group=root
EnvironmentFile=/home/<USER>/weather/weatherblog/celery.env
WorkingDirectory=/home/<USER>/weather/weatherblog
ExecStart=/bin/bash -c 'source /home/<USER>/.bashrc && conda activate weather && celery -A weatherblog worker --loglevel=info --pidfile=/var/run/celery/worker.pid --logfile=/home/<USER>/weather/weatherblog/logs/celery_worker.log --detach'
ExecStop=/bin/kill -s TERM $MAINPID
ExecReload=/bin/kill -s HUP $MAINPID
PIDFile=/var/run/celery/worker.pid
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 创建Celery Beat服务
cat > /etc/systemd/system/celery-beat.service << 'EOF'
[Unit]
Description=Celery Beat Service
After=network.target redis.service

[Service]
Type=forking
User=root
Group=root
EnvironmentFile=/home/<USER>/weather/weatherblog/celery.env
WorkingDirectory=/home/<USER>/weather/weatherblog
ExecStart=/bin/bash -c 'source /home/<USER>/.bashrc && conda activate weather && celery -A weatherblog beat --loglevel=info --pidfile=/var/run/celery/beat.pid --logfile=/home/<USER>/weather/weatherblog/logs/celery_beat.log --detach'
ExecStop=/bin/kill -s TERM $MAINPID
ExecReload=/bin/kill -s HUP $MAINPID
PIDFile=/var/run/celery/beat.pid
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 创建环境变量文件
cat > /home/<USER>/weather/weatherblog/celery.env << 'EOF'
DJANGO_SETTINGS_MODULE=weatherblog.settings
PYTHONPATH=/home/<USER>/weather/weatherblog
EOF

# 创建PID目录
mkdir -p /var/run/celery
chown root:root /var/run/celery

# 重新加载systemd
systemctl daemon-reload

# 启用服务（开机自启）
systemctl enable celery-worker.service
systemctl enable celery-beat.service

echo "✅ Systemd服务配置完成！"
echo ""
echo "使用方法："
echo "启动服务: systemctl start celery-worker celery-beat"
echo "停止服务: systemctl stop celery-worker celery-beat"
echo "查看状态: systemctl status celery-worker celery-beat"
echo "查看日志: journalctl -u celery-worker -f"
echo "查看日志: journalctl -u celery-beat -f"
