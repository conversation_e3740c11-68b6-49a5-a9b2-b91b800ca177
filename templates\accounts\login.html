{% extends 'base.html' %}

{% block title %}登录 - 天气订阅系统{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card">
            <div class="card-body p-5">
                <div class="text-center mb-4">
                    <i class="fas fa-cloud-sun weather-icon"></i>
                    <h3 class="mt-3 mb-0">欢迎回来</h3>
                    <p class="text-muted">登录您的账户</p>
                </div>

                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">
                            <i class="fas fa-envelope me-2"></i>{{ form.username.label }}
                        </label>
                        {{ form.username }}
                        {% if form.username.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.username.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-4">
                        <label for="{{ form.password.id_for_label }}" class="form-label">
                            <i class="fas fa-lock me-2"></i>{{ form.password.label }}
                        </label>
                        {{ form.password }}
                        {% if form.password.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}

                    <button type="submit" class="btn btn-primary w-100 mb-3">
                        <i class="fas fa-sign-in-alt me-2"></i>登录
                    </button>
                </form>

                <div class="text-center">
                    <p class="mb-0">还没有账户？ 
                        <a href="{% url 'accounts:register' %}" class="text-decoration-none">立即注册</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
