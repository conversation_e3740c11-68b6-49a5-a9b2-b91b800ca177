{% extends 'base.html' %}

{% block title %}注册 - 天气订阅系统{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card">
            <div class="card-body p-5">
                <div class="text-center mb-4">
                    <i class="fas fa-user-plus weather-icon"></i>
                    <h3 class="mt-3 mb-0">创建账户</h3>
                    <p class="text-muted">加入天气订阅系统</p>
                </div>

                <form method="post">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">
                            <i class="fas fa-user me-2"></i>{{ form.username.label }}
                        </label>
                        {{ form.username }}
                        {% if form.username.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.username.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.email.id_for_label }}" class="form-label">
                            <i class="fas fa-envelope me-2"></i>{{ form.email.label }}
                        </label>
                        {{ form.email }}
                        {% if form.email.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.password1.id_for_label }}" class="form-label">
                            <i class="fas fa-lock me-2"></i>{{ form.password1.label }}
                        </label>
                        {{ form.password1 }}
                        {% if form.password1.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.password1.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-4">
                        <label for="{{ form.password2.id_for_label }}" class="form-label">
                            <i class="fas fa-lock me-2"></i>{{ form.password2.label }}
                        </label>
                        {{ form.password2 }}
                        {% if form.password2.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.password2.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}

                    <button type="submit" class="btn btn-primary w-100 mb-3">
                        <i class="fas fa-user-plus me-2"></i>注册
                    </button>
                </form>

                <div class="text-center">
                    <p class="mb-0">已有账户？ 
                        <a href="{% url 'accounts:login' %}" class="text-decoration-none">立即登录</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
