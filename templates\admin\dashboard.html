{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}管理员仪表板{% endblock %}

{% block extrahead %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    .dashboard-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    .stat-card {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .stat-number {
        font-size: 2em;
        font-weight: bold;
        color: #0066cc;
    }
    .stat-label {
        color: #666;
        margin-top: 5px;
    }
    .chart-container {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .data-table {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        overflow: hidden;
    }
    .data-table table {
        width: 100%;
        border-collapse: collapse;
    }
    .data-table th,
    .data-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #eee;
    }
    .data-table th {
        background: #f8f9fa;
        font-weight: bold;
    }
    .status-success {
        color: #28a745;
    }
    .status-failed {
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<h1>管理员仪表板</h1>

<!-- 统计卡片 -->
<div class="dashboard-stats">
    <div class="stat-card">
        <div class="stat-number">{{ total_users }}</div>
        <div class="stat-label">总用户数</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">{{ active_users }}</div>
        <div class="stat-label">活跃用户</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">{{ verified_users }}</div>
        <div class="stat-label">已验证用户</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">{{ total_subscriptions }}</div>
        <div class="stat-label">总订阅数</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">{{ active_subscriptions }}</div>
        <div class="stat-label">活跃订阅</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">{{ email_success_rate }}%</div>
        <div class="stat-label">邮件成功率</div>
    </div>
</div>

<!-- 图表区域 -->
<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
    <div class="chart-container">
        <h3>每日新增用户（最近7天）</h3>
        <canvas id="userChart" width="400" height="200"></canvas>
    </div>
    <div class="chart-container">
        <h3>每日邮件发送统计（最近7天）</h3>
        <canvas id="emailChart" width="400" height="200"></canvas>
    </div>
</div>

<!-- 数据表格 -->
<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
    <div class="data-table">
        <h3 style="padding: 15px; margin: 0; background: #f8f9fa; border-bottom: 1px solid #ddd;">最新用户</h3>
        <table>
            <thead>
                <tr>
                    <th>用户名</th>
                    <th>邮箱</th>
                    <th>注册时间</th>
                </tr>
            </thead>
            <tbody>
                {% for user in recent_users %}
                <tr>
                    <td>{{ user.username }}</td>
                    <td>{{ user.email }}</td>
                    <td>{{ user.date_joined|date:"m-d H:i" }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <div class="data-table">
        <h3 style="padding: 15px; margin: 0; background: #f8f9fa; border-bottom: 1px solid #ddd;">最新订阅</h3>
        <table>
            <thead>
                <tr>
                    <th>用户</th>
                    <th>城市</th>
                    <th>订阅时间</th>
                </tr>
            </thead>
            <tbody>
                {% for sub in recent_subscriptions %}
                <tr>
                    <td>{{ sub.user.username }}</td>
                    <td>{{ sub.city.get_full_name }}</td>
                    <td>{{ sub.created_at|date:"m-d H:i" }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px;">
    <div class="data-table">
        <h3 style="padding: 15px; margin: 0; background: #f8f9fa; border-bottom: 1px solid #ddd;">热门城市</h3>
        <table>
            <thead>
                <tr>
                    <th>城市</th>
                    <th>订阅数</th>
                </tr>
            </thead>
            <tbody>
                {% for city in popular_cities %}
                <tr>
                    <td>{{ city.get_full_name }}</td>
                    <td>{{ city.subscription_count }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <div class="data-table">
        <h3 style="padding: 15px; margin: 0; background: #f8f9fa; border-bottom: 1px solid #ddd;">最近邮件日志</h3>
        <table>
            <thead>
                <tr>
                    <th>用户</th>
                    <th>状态</th>
                    <th>发送时间</th>
                </tr>
            </thead>
            <tbody>
                {% for log in recent_email_logs %}
                <tr>
                    <td>{{ log.subscription.user.username }}</td>
                    <td>
                        {% if log.is_sent %}
                            <span class="status-success">✓ 成功</span>
                        {% else %}
                            <span class="status-failed">✗ 失败</span>
                        {% endif %}
                    </td>
                    <td>{{ log.sent_at|date:"m-d H:i" }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<script>
// 用户增长图表
const userCtx = document.getElementById('userChart').getContext('2d');
new Chart(userCtx, {
    type: 'line',
    data: {
        labels: [{% for stat in daily_user_stats %}'{{ stat.date }}'{% if not forloop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: '新增用户',
            data: [{% for stat in daily_user_stats %}{{ stat.count }}{% if not forloop.last %},{% endif %}{% endfor %}],
            borderColor: '#0066cc',
            backgroundColor: 'rgba(0, 102, 204, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// 邮件发送图表
const emailCtx = document.getElementById('emailChart').getContext('2d');
new Chart(emailCtx, {
    type: 'bar',
    data: {
        labels: [{% for stat in daily_email_stats %}'{{ stat.date }}'{% if not forloop.last %},{% endif %}{% endfor %}],
        datasets: [{
            label: '发送成功',
            data: [{% for stat in daily_email_stats %}{{ stat.sent }}{% if not forloop.last %},{% endif %}{% endfor %}],
            backgroundColor: '#28a745'
        }, {
            label: '发送失败',
            data: [{% for stat in daily_email_stats %}{{ stat.failed }}{% if not forloop.last %},{% endif %}{% endfor %}],
            backgroundColor: '#dc3545'
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>
{% endblock %}
