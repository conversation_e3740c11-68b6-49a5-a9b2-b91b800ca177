{% extends 'base.html' %}

{% block title %}取消订阅 - 天气订阅系统{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h4 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>确认取消订阅
                </h4>
            </div>
            <div class="card-body p-4">
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle me-2"></i>
                    您确定要取消订阅以下城市的天气信息吗？此操作不可撤销。
                </div>
                
                <div class="mb-4">
                    <h5 class="mb-3">
                        <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                        {{ subscription.city.get_full_name }}
                    </h5>
                    <p class="text-muted mb-2">
                        <i class="fas fa-envelope me-2"></i>
                        接收邮箱：{{ subscription.email }}
                    </p>
                    <p class="text-muted mb-0">
                        <i class="fas fa-calendar me-2"></i>
                        订阅时间：{{ subscription.created_at|date:"Y年m月d日 H:i" }}
                    </p>
                </div>

                <form method="post">
                    {% csrf_token %}
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'subscriptions:list' %}" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-arrow-left me-2"></i>返回
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>确认取消
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
