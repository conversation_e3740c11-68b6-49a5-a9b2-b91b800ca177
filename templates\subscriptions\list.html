{% extends 'base.html' %}

{% block title %}我的订阅 - 天气订阅系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-bell me-2"></i>我的天气订阅
            </h2>
            <a href="{% url 'subscriptions:add' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>添加订阅
            </a>
        </div>

        {% if subscriptions %}
            <div class="row">
                {% for subscription in subscriptions %}
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                                        {{ subscription.city.get_full_name }}
                                    </h5>
                                    <span class="badge {% if subscription.is_active %}bg-success{% else %}bg-secondary{% endif %}">
                                        {% if subscription.is_active %}激活{% else %}暂停{% endif %}
                                    </span>
                                </div>
                                
                                <p class="card-text">
                                    <i class="fas fa-envelope me-2 text-muted"></i>
                                    <small class="text-muted">{{ subscription.email }}</small>
                                </p>
                                
                                <p class="card-text">
                                    <i class="fas fa-calendar me-2 text-muted"></i>
                                    <small class="text-muted">订阅时间：{{ subscription.created_at|date:"Y-m-d H:i" }}</small>
                                </p>
                            </div>
                            
                            <div class="card-footer bg-transparent">
                                <div class="btn-group w-100" role="group">
                                    <a href="{% url 'subscriptions:toggle' subscription.id %}" 
                                       class="btn btn-sm {% if subscription.is_active %}btn-outline-warning{% else %}btn-outline-success{% endif %}">
                                        <i class="fas {% if subscription.is_active %}fa-pause{% else %}fa-play{% endif %} me-1"></i>
                                        {% if subscription.is_active %}暂停{% else %}激活{% endif %}
                                    </a>
                                    <a href="{% url 'subscriptions:cancel' subscription.id %}" 
                                       class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash me-1"></i>删除
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-bell-slash" style="font-size: 4rem; color: #dee2e6;"></i>
                </div>
                <h4 class="text-muted mb-3">暂无订阅</h4>
                <p class="text-muted mb-4">您还没有订阅任何城市的天气信息</p>
                <a href="{% url 'subscriptions:add' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>立即订阅
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
