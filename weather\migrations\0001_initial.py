# Generated by Django 4.2.7 on 2025-07-27 14:05

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="City",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="城市名称")),
                (
                    "adcode",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="区域编码"
                    ),
                ),
                (
                    "citycode",
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name="城市编码"
                    ),
                ),
                ("level", models.IntegerField(default=0, verbose_name="级别")),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="weather.city",
                        verbose_name="上级城市",
                    ),
                ),
            ],
            options={
                "verbose_name": "城市",
                "verbose_name_plural": "城市",
                "ordering": ["level", "name"],
            },
        ),
        migrations.CreateModel(
            name="WeatherData",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("weather", models.CharField(max_length=50, verbose_name="天气现象")),
                (
                    "temperature",
                    models.CharField(max_length=10, verbose_name="实时气温"),
                ),
                ("winddirection", models.CharField(max_length=20, verbose_name="风向")),
                ("windpower", models.CharField(max_length=10, verbose_name="风力级别")),
                ("humidity", models.CharField(max_length=10, verbose_name="空气湿度")),
                (
                    "reporttime",
                    models.CharField(max_length=50, verbose_name="数据发布时间"),
                ),
                (
                    "forecast_data",
                    models.JSONField(blank=True, null=True, verbose_name="预报数据"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "city",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="weather.city",
                        verbose_name="城市",
                    ),
                ),
            ],
            options={
                "verbose_name": "天气数据",
                "verbose_name_plural": "天气数据",
                "ordering": ["-created_at"],
            },
        ),
    ]
