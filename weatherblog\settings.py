"""
Django settings for weatherblog project.

Generated by 'django-admin startproject' using Django 4.2.7.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

from pathlib import Path
import os

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure--9xt$+ny-r%!rv87r!4et^fr=l!kuks_lj3y!-nvm6xze4ed@^"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = []


# Application definition

INSTALLED_APPS = [
    "simpleui",  # SimpleUI必须放在django.contrib.admin之前
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django_celery_beat",
    "accounts",
    "weather",
    "subscriptions",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "weatherblog.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [BASE_DIR / 'templates'],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "weatherblog.wsgi.application"


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": "weatherblog",
        "USER": "root",
        "PASSWORD": "hubing123",
        "HOST": "localhost",
        "PORT": "3306",
        "OPTIONS": {
            "charset": "utf8mb4",
        },
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "zh-hans"

TIME_ZONE = "Asia/Shanghai"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = "static/"

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# Custom user model
AUTH_USER_MODEL = 'accounts.User'

# Static files
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Email settings
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.qq.com'  # 使用QQ邮箱SMTP
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'  # QQ邮箱
EMAIL_HOST_PASSWORD = 'qglblrluavuzijcg'  # QQ邮箱授权码
DEFAULT_FROM_EMAIL = '<EMAIL>'  # 发件人邮箱

# Celery settings
CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE

# Weather API settings
WEATHER_API_KEY = 'd6a3b63a2d03bba441ed787070a7e308'
WEATHER_API_URL = 'https://restapi.amap.com/v3/weather/weatherInfo'

# Login/Logout URLs
LOGIN_URL = '/accounts/login/'
LOGIN_REDIRECT_URL = '/'
LOGOUT_REDIRECT_URL = '/'

# SimpleUI 配置
SIMPLEUI_CONFIG = {
    'system_keep': False,  # 是否保留系统菜单
    'menu_display': ['用户管理', '天气管理', '订阅管理', '系统管理'],  # 开启排序和过滤功能
    'dynamic': True,  # 设置是否开启动态菜单，默认为False
    'menus': [
        {
            'name': '系统概览',
            'icon': 'fas fa-tachometer-alt',
            'url': '/admin/dashboard/',
        },
        {
            'app': 'accounts',
            'name': '用户管理',
            'icon': 'fas fa-users',
            'models': [
                {
                    'name': '用户',
                    'icon': 'fas fa-user',
                    'url': '/admin/accounts/user/'
                },
                {
                    'name': '邮箱验证',
                    'icon': 'fas fa-envelope-open',
                    'url': '/admin/accounts/emailverification/'
                }
            ]
        },
        {
            'app': 'weather',
            'name': '天气管理',
            'icon': 'fas fa-cloud-sun',
            'models': [
                {
                    'name': '城市管理',
                    'icon': 'fas fa-map-marker-alt',
                    'url': '/admin/weather/city/'
                },
                {
                    'name': '天气数据',
                    'icon': 'fas fa-thermometer-half',
                    'url': '/admin/weather/weatherdata/'
                }
            ]
        },
        {
            'app': 'subscriptions',
            'name': '订阅管理',
            'icon': 'fas fa-bell',
            'models': [
                {
                    'name': '订阅列表',
                    'icon': 'fas fa-list',
                    'url': '/admin/subscriptions/subscription/'
                },
                {
                    'name': '邮件日志',
                    'icon': 'fas fa-envelope',
                    'url': '/admin/subscriptions/emaillog/'
                }
            ]
        },
        {
            'name': '定时任务',
            'icon': 'fas fa-clock',
            'models': [
                {
                    'name': '周期任务',
                    'icon': 'fas fa-calendar-alt',
                    'url': '/admin/django_celery_beat/periodictask/'
                },
                {
                    'name': '定时计划',
                    'icon': 'fas fa-stopwatch',
                    'url': '/admin/django_celery_beat/crontabschedule/'
                }
            ]
        }
    ]
}

# SimpleUI 主题和样式配置
SIMPLEUI_DEFAULT_THEME = 'admin.lte.css'  # 默认主题
SIMPLEUI_HOME_PAGE = '/admin/dashboard/'  # 设置首页
SIMPLEUI_HOME_TITLE = '天气订阅系统管理后台'  # 首页标题
SIMPLEUI_HOME_ICON = 'fas fa-cloud-sun'  # 首页图标
SIMPLEUI_LOGO = 'https://avatars2.githubusercontent.com/u/13655483?s=60&v=4'  # Logo

# 隐藏右侧SimpleUI广告
SIMPLEUI_HOME_INFO = False
SIMPLEUI_ANALYSIS = False

# 自定义后台标题
SIMPLEUI_DEFAULT_ICON = True
SIMPLEUI_ICON = {
    '用户管理': 'fas fa-users',
    '天气管理': 'fas fa-cloud-sun',
    '订阅管理': 'fas fa-bell',
    '定时任务': 'fas fa-clock',
}
